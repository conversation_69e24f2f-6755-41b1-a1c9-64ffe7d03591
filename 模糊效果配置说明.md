# PDF缩略图模糊效果配置说明

## 当前模糊设置

程序已经配置为生成**模糊的缩略图**，能看出页面结构但看不清具体文字内容。

### 默认模糊参数

在 `config.py` 文件中的设置：

```python
PDF_CONFIG = {
    # 基础设置
    'thumbnail_size': (120, 160),    # 较小尺寸 -> 更模糊
    'dpi': 50,                       # 低DPI -> 更模糊
    'output_quality': 60,            # 低质量 -> 更模糊
    
    # 模糊效果设置
    'blur_radius': 0.8,              # 高斯模糊半径
    'contrast_factor': 0.7,          # 对比度因子
    'sharpness_factor': 0.5          # 锐度因子
}
```

## 模糊效果说明

### 1. 尺寸模糊 (thumbnail_size)
- **当前**: 120×160像素（较小）
- **效果**: 基础模糊，文字难以辨认
- **调整**: 更小 = 更模糊，更大 = 更清晰

### 2. DPI模糊 (dpi)
- **当前**: 50 DPI（很低）
- **效果**: PDF转图片时分辨率低
- **调整**: 更低 = 更模糊，更高 = 更清晰

### 3. 质量模糊 (output_quality)
- **当前**: 60%（中低质量）
- **效果**: JPEG压缩产生模糊
- **调整**: 更低 = 更模糊，更高 = 更清晰

### 4. 高斯模糊 (blur_radius)
- **当前**: 0.8（轻微模糊）
- **效果**: 软化边缘，模糊文字
- **调整**: 0-2之间，越大越模糊

### 5. 对比度降低 (contrast_factor)
- **当前**: 0.7（降低30%对比度）
- **效果**: 文字与背景对比度降低
- **调整**: 0-1之间，越小越模糊

### 6. 锐度降低 (sharpness_factor)
- **当前**: 0.5（降低50%锐度）
- **效果**: 边缘变软，细节模糊
- **调整**: 0-1之间，越小越模糊

## 模糊程度调整

### 轻度模糊（能看清大标题）
```python
'thumbnail_size': (150, 200),
'dpi': 72,
'blur_radius': 0.5,
'contrast_factor': 0.8,
'sharpness_factor': 0.7
```

### 中度模糊（当前设置，看不清文字）
```python
'thumbnail_size': (120, 160),
'dpi': 50,
'blur_radius': 0.8,
'contrast_factor': 0.7,
'sharpness_factor': 0.5
```

### 重度模糊（只能看出页面轮廓）
```python
'thumbnail_size': (100, 140),
'dpi': 36,
'blur_radius': 1.2,
'contrast_factor': 0.5,
'sharpness_factor': 0.3
```

## 实际效果

使用当前设置生成的缩略图将具有以下特点：

✅ **能看到的内容**：
- 页面整体布局
- 图片和图表的大致位置
- 文本块的分布
- 页面边框和分割线

❌ **看不清的内容**：
- 具体文字内容
- 小字和细节
- 表格中的数据
- 图片中的细节

## 如何修改模糊程度

1. **打开配置文件**：编辑 `config.py`
2. **调整参数**：根据需要修改上述参数
3. **重启程序**：保存后重新运行程序
4. **测试效果**：处理一个PDF文件查看效果

## 推荐设置场景

### 文档预览（能看出结构但保护内容）
```python
'dpi': 50,
'blur_radius': 0.8,
'contrast_factor': 0.7
```

### 版权保护（严重模糊）
```python
'dpi': 36,
'blur_radius': 1.5,
'contrast_factor': 0.5
```

### 快速浏览（轻微模糊）
```python
'dpi': 72,
'blur_radius': 0.3,
'contrast_factor': 0.9
```

## 注意事项

- 模糊程度过高可能导致完全看不出内容
- DPI过低可能影响页面结构识别
- 建议先用单个文件测试效果再批量处理
- 不同类型的PDF（文字多/图片多）效果可能不同
