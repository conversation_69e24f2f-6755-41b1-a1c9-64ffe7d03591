#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF处理核心模块
负责PDF转图片和图片拼接功能
"""

import os
import io
import fitz  # PyMuPDF
from PIL import Image, ImageDraw
import tempfile
import shutil

try:
    from config import PDF_CONFIG, PERFORMANCE_CONFIG
except ImportError:
    # 如果没有配置文件，使用默认配置
    PDF_CONFIG = {
        'max_pages': 30,
        'grid_layout': (6, 5),
        'thumbnail_size': (200, 280),
        'dpi': 100,
        'output_quality': 85,
        'output_format': 'JPEG',
        'filename_suffix': '_thumbnail'
    }
    PERFORMANCE_CONFIG = {
        'memory_optimize': True
    }


class PDFProcessor:
    def __init__(self):
        # 从配置文件加载设置
        self.max_pages = PDF_CONFIG['max_pages']
        self.grid_cols, self.grid_rows = PDF_CONFIG['grid_layout']
        self.thumbnail_size = PDF_CONFIG['thumbnail_size']
        self.dpi = PDF_CONFIG['dpi']
        self.output_quality = PDF_CONFIG['output_quality']
        self.output_format = PDF_CONFIG['output_format']
        self.filename_suffix = PDF_CONFIG['filename_suffix']
        
    def process_pdf(self, pdf_path, log_callback=None):
        """
        处理单个PDF文件
        
        Args:
            pdf_path: PDF文件路径
            log_callback: 日志回调函数
            
        Returns:
            bool: 处理是否成功
        """
        try:
            if log_callback:
                log_callback(f"开始处理: {os.path.basename(pdf_path)}")
                
            # 检查文件是否存在
            if not os.path.exists(pdf_path):
                if log_callback:
                    log_callback(f"错误: 文件不存在 - {pdf_path}")
                return False
                
            # 打开PDF文件
            try:
                pdf_document = fitz.open(pdf_path)
            except Exception as e:
                if log_callback:
                    log_callback(f"错误: 无法打开PDF文件 - {str(e)}")
                return False
                
            # 获取页数
            total_pages = len(pdf_document)
            if log_callback:
                log_callback(f"PDF总页数: {total_pages}")
                
            if total_pages == 0:
                if log_callback:
                    log_callback("错误: PDF文件为空")
                pdf_document.close()
                return False
                
            # 确定要处理的页数
            pages_to_process = min(total_pages, self.max_pages)
            if log_callback:
                log_callback(f"将处理前 {pages_to_process} 页")
                
            # 创建临时目录存储图片
            temp_dir = tempfile.mkdtemp()
            
            try:
                # 转换PDF页面为图片
                images = []
                for page_num in range(pages_to_process):
                    if log_callback:
                        log_callback(f"正在转换第 {page_num + 1}/{pages_to_process} 页...")
                        
                    page = pdf_document[page_num]
                    
                    # 设置缩放矩阵以控制输出图片质量
                    mat = fitz.Matrix(self.dpi / 72, self.dpi / 72)
                    pix = page.get_pixmap(matrix=mat)
                    
                    # 转换为PIL Image
                    img_data = pix.tobytes("ppm")
                    img = Image.open(io.BytesIO(img_data))
                    
                    # 调整图片大小为缩略图尺寸
                    img.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
                    images.append(img)
                    
                pdf_document.close()
                
                if log_callback:
                    log_callback("PDF页面转换完成，开始拼接图片...")
                    
                # 拼接图片
                result_image = self.create_thumbnail_grid(images)
                
                # 保存拼接后的图片
                output_path = self.get_output_path(pdf_path)
                result_image.save(output_path, self.output_format, quality=self.output_quality)
                
                if log_callback:
                    log_callback(f"缩略图已保存: {output_path}")
                    
                return True
                
            finally:
                # 清理临时目录
                shutil.rmtree(temp_dir, ignore_errors=True)
                
        except Exception as e:
            if log_callback:
                log_callback(f"处理失败: {str(e)}")
            return False
            
    def create_thumbnail_grid(self, images):
        """
        创建6x5的缩略图网格
        
        Args:
            images: PIL Image对象列表
            
        Returns:
            PIL.Image: 拼接后的图片
        """
        # 计算输出图片尺寸
        output_width = self.grid_cols * self.thumbnail_size[0]
        output_height = self.grid_rows * self.thumbnail_size[1]
        
        # 创建白色背景的输出图片
        result = Image.new('RGB', (output_width, output_height), 'white')
        
        # 拼接图片
        for i, img in enumerate(images):
            if i >= self.grid_cols * self.grid_rows:  # 最多30张图片
                break
                
            # 计算当前图片在网格中的位置
            row = i // self.grid_cols
            col = i % self.grid_cols
            
            # 计算粘贴位置
            x = col * self.thumbnail_size[0]
            y = row * self.thumbnail_size[1]
            
            # 确保图片尺寸正确
            if img.size != self.thumbnail_size:
                # 创建一个标准尺寸的白色背景
                thumb = Image.new('RGB', self.thumbnail_size, 'white')
                
                # 计算居中位置
                paste_x = (self.thumbnail_size[0] - img.size[0]) // 2
                paste_y = (self.thumbnail_size[1] - img.size[1]) // 2
                
                # 将图片粘贴到中心位置
                thumb.paste(img, (paste_x, paste_y))
                img = thumb
                
            # 粘贴到结果图片
            result.paste(img, (x, y))
            
        return result
        
    def get_output_path(self, pdf_path):
        """
        生成输出文件路径
        
        Args:
            pdf_path: 原PDF文件路径
            
        Returns:
            str: 输出图片路径
        """
        # 获取PDF文件的目录和文件名
        pdf_dir = os.path.dirname(pdf_path)
        pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
        
        # 生成输出文件名
        file_ext = '.jpg' if self.output_format == 'JPEG' else f'.{self.output_format.lower()}'
        output_filename = f"{pdf_name}{self.filename_suffix}{file_ext}"
        output_path = os.path.join(pdf_dir, output_filename)
        
        return output_path
