#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF缩略图生成工具配置文件
可以在这里修改程序的默认设置
"""

# PDF处理设置
PDF_CONFIG = {
    # 最多处理的页数
    'max_pages': 30,

    # 网格布局 (列数, 行数)
    'grid_layout': (6, 5),

    # 单个缩略图尺寸 (宽度, 高度) - 降低尺寸使图片更模糊
    'thumbnail_size': (120, 160),

    # PDF转图片的DPI设置 - 降低DPI使图片更模糊
    'dpi': 50,

    # 输出图片质量 (1-100) - 降低质量使图片更模糊
    'output_quality': 60,

    # 输出图片格式
    'output_format': 'JPEG',

    # 缩略图文件名后缀
    'filename_suffix': '_thumbnail',

    # 模糊效果设置
    'blur_radius': 0.8,        # 高斯模糊半径，0-2之间，越大越模糊
    'contrast_factor': 0.7,    # 对比度因子，0-1之间，越小越模糊
    'sharpness_factor': 0.5    # 锐度因子，0-1之间，越小越模糊
}

# GUI界面设置
GUI_CONFIG = {
    # 窗口默认大小
    'window_size': '600x400',
    
    # 窗口标题
    'window_title': 'PDF缩略图批量生成工具',
    
    # 是否允许调整窗口大小
    'resizable': True,
    
    # 日志文本框高度
    'log_height': 10,
    
    # 界面主题
    'theme': 'clam'
}

# 文件处理设置
FILE_CONFIG = {
    # 支持的PDF文件扩展名
    'pdf_extensions': ['.pdf'],
    
    # 是否递归扫描子文件夹
    'recursive_scan': True,
    
    # 临时文件清理
    'cleanup_temp': True,
    
    # 是否覆盖已存在的缩略图
    'overwrite_existing': True
}

# 性能设置
PERFORMANCE_CONFIG = {
    # 是否使用多线程处理
    'use_threading': True,
    
    # 内存优化模式
    'memory_optimize': True,
    
    # 批量处理时的进度更新频率
    'progress_update_frequency': 1
}
