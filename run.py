#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF缩略图生成工具启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main import main
    main()
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所有依赖包:")
    print("pip install -r requirements.txt")
    input("按回车键退出...")
except Exception as e:
    print(f"程序运行错误: {e}")
    input("按回车键退出...")
