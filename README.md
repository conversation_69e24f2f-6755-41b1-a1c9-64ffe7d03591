# PDF缩略图批量生成工具

这是一个用于批量生成PDF文件缩略图的Python工具，具有简洁的GUI界面。

## 功能特点

- **双模式处理**: 支持单个文件处理和批量处理模式
- **多级清晰度**: 提供高清、标准、模糊、极度模糊四种输出质量选择
- **智能拼接**: 自动将PDF前30页转换为图片，并按6×5格式拼接成一张缩略图
- **递归扫描**: 批量模式可以递归扫描文件夹及其子文件夹中的所有PDF文件
- **完成提示**: 处理完成后弹出提示对话框，可直接打开输出文件夹
- **实时反馈**: 提供详细的处理进度和日志信息
- **用户友好**: 简洁直观的GUI界面，操作简单

## 安装依赖

在运行程序之前，请先安装所需的依赖包：

```bash
pip install -r requirements.txt
```

### 依赖包说明

- `PyMuPDF`: 用于PDF文件处理和页面转换
- `Pillow`: 用于图像处理和拼接
- `tkinter-tooltip`: 用于GUI界面增强（可选）

## 使用方法

### 方法1: 直接运行主程序
```bash
python main.py
```

### 方法2: 使用启动脚本
```bash
python run.py
```

## 使用说明

1. **选择处理模式**:
   - **单个文件处理**: 选择一个PDF文件进行处理
   - **批量处理**: 选择一个文件夹，程序会自动扫描其中的所有PDF文件

2. **选择输出清晰度**:
   - **高清**: 文字清晰可读，适合需要阅读内容的场景
   - **标准**: 文字基本可读，质量适中
   - **模糊**: 能看出页面结构，文字模糊不可读（默认）
   - **极度模糊**: 只能看出页面轮廓，内容完全模糊

3. **选择文件或文件夹**:
   - 点击"浏览"按钮选择要处理的PDF文件或包含PDF文件的文件夹

4. **开始处理**:
   - 点击"开始处理"按钮开始转换
   - 程序会显示详细的处理进度和日志信息

5. **查看结果**:
   - 处理完成后会弹出提示对话框
   - 点击"打开输出文件夹"可直接查看生成的缩略图
   - 文件名格式: `原文件名_thumbnail.jpg`

## 输出格式

- **图片格式**: JPEG
- **拼接布局**: 6列×5行（最多30页）
- **单页尺寸**: 120×160像素（故意较小以产生模糊效果）
- **总图尺寸**: 720×800像素
- **图片质量**: 模糊效果（能看出页面结构但看不清文字内容）
- **模糊特点**:
  - ✅ 能看到页面布局、图表位置、文本块分布
  - ❌ 看不清具体文字内容、表格数据、图片细节

## 模糊效果说明

程序**故意生成模糊的缩略图**，具有以下特点：
- 能看出PDF页面的整体结构和布局
- 文字内容被模糊处理，无法清晰阅读
- 适合用于文档预览和版权保护
- 可通过修改 `config.py` 调整模糊程度

详细的模糊效果配置说明请查看 `模糊效果配置说明.md`

## 注意事项

- 程序只处理PDF文件的前30页
- 如果PDF页数少于30页，会按实际页数处理
- 生成的缩略图**故意模糊**，主要用于快速预览页面结构
- 批量处理时会递归扫描所有子文件夹
- 处理过程中请勿关闭程序窗口

## 文件结构

```
├── main.py                    # 主程序文件（GUI界面）
├── pdf_processor.py           # PDF处理核心模块
├── config.py                  # 配置文件（包含模糊效果设置）
├── run.py                     # 启动脚本
├── install.py                 # 自动安装脚本
├── test_gui.py               # GUI测试脚本
├── 启动程序.bat               # Windows批处理启动文件
├── requirements.txt          # 依赖包列表
├── README.md                 # 详细说明文档
├── 使用示例.md                # 使用示例和教程
└── 模糊效果配置说明.md         # 模糊效果详细配置说明
```

## 故障排除

### 常见问题

1. **导入错误**: 请确保已安装所有依赖包
2. **PDF无法打开**: 检查PDF文件是否损坏或受密码保护
3. **内存不足**: 处理大量PDF文件时可能需要更多内存
4. **权限错误**: 确保对目标文件夹有写入权限

### 系统要求

- Python 3.7+
- Windows/macOS/Linux
- 至少512MB可用内存
- 足够的磁盘空间存储生成的图片

## 许可证

本项目仅供学习和个人使用。
