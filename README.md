# PDF缩略图批量生成工具

这是一个用于批量生成PDF文件缩略图的Python工具，具有简洁的GUI界面。

## 功能特点

- **双模式处理**: 支持单个文件处理和批量处理模式
- **智能拼接**: 自动将PDF前30页转换为图片，并按6×5格式拼接成一张缩略图
- **递归扫描**: 批量模式可以递归扫描文件夹及其子文件夹中的所有PDF文件
- **实时反馈**: 提供详细的处理进度和日志信息
- **用户友好**: 简洁直观的GUI界面，操作简单

## 安装依赖

在运行程序之前，请先安装所需的依赖包：

```bash
pip install -r requirements.txt
```

### 依赖包说明

- `PyMuPDF`: 用于PDF文件处理和页面转换
- `Pillow`: 用于图像处理和拼接
- `tkinter-tooltip`: 用于GUI界面增强（可选）

## 使用方法

### 方法1: 直接运行主程序
```bash
python main.py
```

### 方法2: 使用启动脚本
```bash
python run.py
```

## 使用说明

1. **选择处理模式**:
   - **单个文件处理**: 选择一个PDF文件进行处理
   - **批量处理**: 选择一个文件夹，程序会自动扫描其中的所有PDF文件

2. **选择文件或文件夹**:
   - 点击"浏览"按钮选择要处理的PDF文件或包含PDF文件的文件夹

3. **开始处理**:
   - 点击"开始处理"按钮开始转换
   - 程序会显示详细的处理进度和日志信息

4. **查看结果**:
   - 生成的缩略图会保存在原PDF文件相同的文件夹中
   - 文件名格式: `原文件名_thumbnail.jpg`

## 输出格式

- **图片格式**: JPEG
- **拼接布局**: 6列×5行（最多30页）
- **单页尺寸**: 200×280像素
- **总图尺寸**: 1200×1400像素
- **图片质量**: 中等（适合预览，文件大小适中）

## 注意事项

- 程序只处理PDF文件的前30页
- 如果PDF页数少于30页，会按实际页数处理
- 生成的缩略图质量适中，主要用于快速预览
- 批量处理时会递归扫描所有子文件夹
- 处理过程中请勿关闭程序窗口

## 文件结构

```
├── main.py              # 主程序文件（GUI界面）
├── pdf_processor.py     # PDF处理核心模块
├── run.py              # 启动脚本
├── requirements.txt    # 依赖包列表
└── README.md          # 说明文档
```

## 故障排除

### 常见问题

1. **导入错误**: 请确保已安装所有依赖包
2. **PDF无法打开**: 检查PDF文件是否损坏或受密码保护
3. **内存不足**: 处理大量PDF文件时可能需要更多内存
4. **权限错误**: 确保对目标文件夹有写入权限

### 系统要求

- Python 3.7+
- Windows/macOS/Linux
- 至少512MB可用内存
- 足够的磁盘空间存储生成的图片

## 许可证

本项目仅供学习和个人使用。
