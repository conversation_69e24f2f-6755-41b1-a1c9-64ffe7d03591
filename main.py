#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF缩略图批量生成工具
功能：将PDF文件的前30页转换为图片并拼接成6x5的缩略图
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from pdf_processor import PDFProcessor


class PDFThumbnailApp:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF缩略图批量生成工具")
        self.root.geometry("600x400")
        self.root.resizable(True, True)
        
        # 创建PDF处理器实例
        self.processor = PDFProcessor()
        
        # 设置样式
        self.setup_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="PDF缩略图生成工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 处理模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="处理模式", padding="10")
        mode_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.mode_var = tk.StringVar(value="single")
        
        single_radio = ttk.Radiobutton(mode_frame, text="单个文件处理", 
                                      variable=self.mode_var, value="single",
                                      command=self.on_mode_change)
        single_radio.grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        
        batch_radio = ttk.Radiobutton(mode_frame, text="批量处理", 
                                     variable=self.mode_var, value="batch",
                                     command=self.on_mode_change)
        batch_radio.grid(row=0, column=1, sticky=tk.W)
        
        # 文件/文件夹选择
        select_frame = ttk.Frame(main_frame)
        select_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        select_frame.columnconfigure(1, weight=1)
        
        self.select_label = ttk.Label(select_frame, text="选择PDF文件:")
        self.select_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.path_var = tk.StringVar()
        self.path_entry = ttk.Entry(select_frame, textvariable=self.path_var, state="readonly")
        self.path_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        self.browse_button = ttk.Button(select_frame, text="浏览", command=self.browse_file)
        self.browse_button.grid(row=0, column=2)
        
        # 进度显示
        progress_frame = ttk.LabelFrame(main_frame, text="处理进度", padding="10")
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.StringVar(value="等待开始...")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 日志显示
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=(10, 0))
        
        self.start_button = ttk.Button(button_frame, text="开始处理", 
                                      command=self.start_processing, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_button = ttk.Button(button_frame, text="清空日志", command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT)
        
    def on_mode_change(self):
        """处理模式改变事件"""
        if self.mode_var.get() == "single":
            self.select_label.config(text="选择PDF文件:")
        else:
            self.select_label.config(text="选择文件夹:")
        self.path_var.set("")
        
    def browse_file(self):
        """浏览文件或文件夹"""
        if self.mode_var.get() == "single":
            filename = filedialog.askopenfilename(
                title="选择PDF文件",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
            )
            if filename:
                self.path_var.set(filename)
        else:
            dirname = filedialog.askdirectory(title="选择包含PDF文件的文件夹")
            if dirname:
                self.path_var.set(dirname)
                
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def update_progress(self, message):
        """更新进度显示"""
        self.progress_var.set(message)
        
    def start_processing(self):
        """开始处理"""
        path = self.path_var.get().strip()
        if not path:
            messagebox.showerror("错误", "请先选择文件或文件夹")
            return
            
        if self.mode_var.get() == "single" and not os.path.isfile(path):
            messagebox.showerror("错误", "选择的文件不存在")
            return
            
        if self.mode_var.get() == "batch" and not os.path.isdir(path):
            messagebox.showerror("错误", "选择的文件夹不存在")
            return
            
        # 在新线程中处理，避免界面卡死
        self.start_button.config(state="disabled")
        self.progress_bar.start()
        
        thread = threading.Thread(target=self.process_files, args=(path,))
        thread.daemon = True
        thread.start()
        
    def process_files(self, path):
        """处理文件的线程函数"""
        try:
            if self.mode_var.get() == "single":
                self.process_single_file(path)
            else:
                self.process_batch_files(path)
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理过程中发生错误: {str(e)}"))
        finally:
            self.root.after(0, self.processing_finished)
            
    def process_single_file(self, pdf_path):
        """处理单个PDF文件"""
        self.root.after(0, lambda: self.update_progress("正在处理单个文件..."))
        self.root.after(0, lambda: self.log_message(f"开始处理文件: {pdf_path}"))
        
        success = self.processor.process_pdf(pdf_path, self.log_callback)
        
        if success:
            self.root.after(0, lambda: self.log_message("文件处理完成!"))
            self.root.after(0, lambda: self.update_progress("处理完成"))
        else:
            self.root.after(0, lambda: self.log_message("文件处理失败!"))
            self.root.after(0, lambda: self.update_progress("处理失败"))
            
    def process_batch_files(self, folder_path):
        """批量处理PDF文件"""
        self.root.after(0, lambda: self.update_progress("正在扫描文件夹..."))
        self.root.after(0, lambda: self.log_message(f"开始扫描文件夹: {folder_path}"))
        
        pdf_files = self.find_pdf_files(folder_path)
        
        if not pdf_files:
            self.root.after(0, lambda: self.log_message("未找到PDF文件"))
            self.root.after(0, lambda: self.update_progress("未找到文件"))
            return
            
        self.root.after(0, lambda: self.log_message(f"找到 {len(pdf_files)} 个PDF文件"))
        
        success_count = 0
        for i, pdf_path in enumerate(pdf_files, 1):
            self.root.after(0, lambda i=i, total=len(pdf_files): 
                          self.update_progress(f"正在处理第 {i}/{total} 个文件..."))
            
            if self.processor.process_pdf(pdf_path, self.log_callback):
                success_count += 1
                
        self.root.after(0, lambda: self.log_message(f"批量处理完成! 成功: {success_count}/{len(pdf_files)}"))
        self.root.after(0, lambda: self.update_progress("批量处理完成"))
        
    def find_pdf_files(self, folder_path):
        """递归查找文件夹中的所有PDF文件"""
        pdf_files = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))
        return pdf_files
        
    def log_callback(self, message):
        """日志回调函数"""
        self.root.after(0, lambda: self.log_message(message))
        
    def processing_finished(self):
        """处理完成后的清理工作"""
        self.progress_bar.stop()
        self.start_button.config(state="normal")
        
    def on_closing(self):
        """窗口关闭事件"""
        self.root.destroy()


def main():
    root = tk.Tk()
    app = PDFThumbnailApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
