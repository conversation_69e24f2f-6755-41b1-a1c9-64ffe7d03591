# PDF缩略图生成工具使用示例

## 快速开始

### 1. 安装环境
```bash
# 运行安装脚本
python install.py
```

### 2. 启动程序
```bash
# 方法1: 直接运行
python main.py

# 方法2: 使用启动脚本
python run.py

# 方法3: Windows用户可以双击
启动程序.bat
```

## 使用场景示例

### 场景1: 处理单个PDF文件

1. 启动程序
2. 选择"单个文件处理"模式
3. 点击"浏览"按钮，选择一个PDF文件
4. 点击"开始处理"
5. 等待处理完成，查看生成的缩略图

**示例输出:**
- 原文件: `报告.pdf`
- 生成文件: `报告_thumbnail.jpg`

### 场景2: 批量处理文件夹中的PDF

1. 启动程序
2. 选择"批量处理"模式
3. 点击"浏览"按钮，选择包含PDF文件的文件夹
4. 点击"开始处理"
5. 程序会自动扫描文件夹及子文件夹中的所有PDF文件

**文件夹结构示例:**
```
文档文件夹/
├── 报告1.pdf          → 生成 报告1_thumbnail.jpg
├── 报告2.pdf          → 生成 报告2_thumbnail.jpg
├── 子文件夹1/
│   ├── 文档A.pdf      → 生成 文档A_thumbnail.jpg
│   └── 文档B.pdf      → 生成 文档B_thumbnail.jpg
└── 子文件夹2/
    └── 资料.pdf       → 生成 资料_thumbnail.jpg
```

## 自定义配置

你可以修改 `config.py` 文件来自定义程序行为：

### 修改网格布局
```python
# 改为4x4布局（16页）
'grid_layout': (4, 4),
```

### 修改缩略图尺寸
```python
# 更大的缩略图
'thumbnail_size': (300, 400),
```

### 修改输出质量
```python
# 更高质量
'output_quality': 95,
```

### 修改处理页数
```python
# 只处理前20页
'max_pages': 20,
```

## 处理结果说明

### 输出格式
- **默认格式**: JPEG
- **默认质量**: 85%
- **默认布局**: 6列×5行（30页）
- **单页尺寸**: 200×280像素
- **总图尺寸**: 1200×1400像素

### 文件命名规则
- **单个文件**: `原文件名_thumbnail.jpg`
- **批量处理**: 每个PDF都在其所在文件夹生成对应的缩略图

### 处理逻辑
1. 读取PDF文件的前30页（可配置）
2. 将每页转换为200×280像素的图片
3. 按6×5网格排列拼接
4. 保存为JPEG格式的缩略图

## 常见问题解决

### Q: 程序启动失败
**A:** 检查是否已安装依赖包
```bash
python install.py
```

### Q: PDF文件无法处理
**A:** 可能原因：
- PDF文件损坏
- PDF文件受密码保护
- 文件路径包含特殊字符

### Q: 内存不足
**A:** 处理大量PDF时：
- 减少同时处理的文件数量
- 降低DPI设置（在config.py中修改）
- 减小缩略图尺寸

### Q: 生成的图片质量不好
**A:** 调整配置：
```python
# 提高DPI
'dpi': 150,

# 增大缩略图尺寸
'thumbnail_size': (250, 350),

# 提高输出质量
'output_quality': 95,
```

## 性能优化建议

### 大批量处理
- 建议分批处理，每次不超过100个文件
- 确保有足够的磁盘空间
- 关闭其他占用内存的程序

### 提高处理速度
- 降低DPI设置（如改为72）
- 减小缩略图尺寸
- 使用SSD硬盘

### 节省存储空间
- 降低输出质量（如改为70）
- 使用更小的缩略图尺寸
- 定期清理不需要的缩略图
