# PDF缩略图清晰度选择指南

## 功能概述

程序新增了**清晰度选择功能**，用户可以根据不同需求选择合适的输出质量。通过界面上的下拉框，可以在4种清晰度级别中选择。

## 清晰度级别详解

### 🔍 高清模式
```
图片尺寸: 300×400像素
总图尺寸: 1800×2000像素
DPI: 150
质量: 95%
模糊: 无
对比度: 100%
锐度: 120%
```

**适用场景:**
- 需要清晰阅读PDF内容
- 制作高质量的文档预览
- 打印或展示用途
- 文档归档和管理

**效果特点:**
- ✅ 文字清晰可读
- ✅ 图片细节丰富
- ✅ 表格数据清晰
- ❌ 文件较大

### 📄 标准模式
```
图片尺寸: 200×280像素
总图尺寸: 1200×1400像素
DPI: 100
质量: 85%
模糊: 轻微(0.2)
对比度: 90%
锐度: 100%
```

**适用场景:**
- 一般文档预览
- 网页展示
- 文档管理系统
- 平衡质量和文件大小

**效果特点:**
- ✅ 文字基本可读
- ✅ 质量适中
- ✅ 文件大小合理
- ⚠️ 小字可能略模糊

### 🌫️ 模糊模式（默认推荐）
```
图片尺寸: 120×160像素
总图尺寸: 720×800像素
DPI: 50
质量: 60%
模糊: 中等(0.8)
对比度: 70%
锐度: 50%
```

**适用场景:**
- 文档结构预览
- 保护内容隐私
- 版权保护
- 快速浏览页面布局

**效果特点:**
- ✅ 能看出页面结构
- ✅ 图表位置清晰
- ✅ 文件小，加载快
- ❌ 文字内容不可读

### 🌁 极度模糊模式
```
图片尺寸: 80×120像素
总图尺寸: 480×600像素
DPI: 36
质量: 45%
模糊: 重度(1.5)
对比度: 50%
锐度: 30%
```

**适用场景:**
- 严格版权保护
- 高度隐私要求
- 仅显示页面轮廓
- 最小文件大小需求

**效果特点:**
- ✅ 只能看出页面轮廓
- ✅ 文件极小
- ✅ 加载极快
- ❌ 内容完全不可读

## 使用建议

### 根据用途选择

| 用途 | 推荐清晰度 | 原因 |
|------|------------|------|
| 文档阅读 | 高清 | 需要清晰阅读文字 |
| 网站展示 | 标准 | 平衡质量和加载速度 |
| 内容保护 | 模糊 | 显示结构但保护内容 |
| 版权保护 | 极度模糊 | 严格保护知识产权 |

### 根据文档类型选择

| 文档类型 | 推荐清晰度 | 说明 |
|----------|------------|------|
| 文字文档 | 高清/标准 | 文字需要清晰显示 |
| 图表报告 | 标准/模糊 | 重点是结构和布局 |
| 机密文件 | 模糊/极度模糊 | 保护敏感信息 |
| 学术论文 | 模糊 | 显示结构，保护版权 |

### 根据存储需求选择

| 存储要求 | 推荐清晰度 | 文件大小估算 |
|----------|------------|--------------|
| 不限制 | 高清 | 200-500KB |
| 中等限制 | 标准 | 100-200KB |
| 严格限制 | 模糊 | 50-100KB |
| 极度限制 | 极度模糊 | 20-50KB |

## 界面操作说明

### 选择清晰度
1. 在"输出清晰度"区域找到下拉框
2. 点击下拉箭头查看所有选项
3. 选择合适的清晰度级别
4. 下方会显示对应的效果说明

### 清晰度说明文字
选择不同清晰度后，界面会显示相应的说明：
- **高清**: "高清晰度，文字清晰可读"
- **标准**: "标准清晰度，文字基本可读"
- **模糊**: "能看出页面结构，文字模糊不可读"
- **极度模糊**: "只能看出页面轮廓，内容完全模糊"

## 处理完成提示

### 完成对话框功能
处理完成后会自动弹出对话框，包含：
- ✅ 成功提示图标和文字
- 📁 输出文件夹路径显示
- 🔗 "打开输出文件夹"按钮
- ✔️ "确定"按钮关闭对话框

### 打开文件夹功能
- **Windows**: 使用资源管理器打开
- **macOS**: 使用Finder打开
- **Linux**: 使用默认文件管理器打开

## 技术参数对比

| 参数 | 高清 | 标准 | 模糊 | 极度模糊 |
|------|------|------|------|----------|
| 单页尺寸 | 300×400 | 200×280 | 120×160 | 80×120 |
| 总图尺寸 | 1800×2000 | 1200×1400 | 720×800 | 480×600 |
| DPI | 150 | 100 | 50 | 36 |
| JPEG质量 | 95% | 85% | 60% | 45% |
| 高斯模糊 | 0 | 0.2 | 0.8 | 1.5 |
| 对比度 | 100% | 90% | 70% | 50% |
| 锐度 | 120% | 100% | 50% | 30% |

## 注意事项

1. **默认选择**: 程序启动时默认选择"模糊"模式
2. **实时预览**: 选择不同清晰度时会显示效果说明
3. **批量处理**: 批量处理时所有文件使用相同的清晰度设置
4. **文件命名**: 不同清晰度生成的文件名相同，会覆盖之前的结果
5. **性能影响**: 高清模式处理速度较慢，文件较大

## 常见问题

**Q: 可以同时生成多种清晰度吗？**
A: 目前不支持，每次处理只能选择一种清晰度。如需多种清晰度，请分别运行程序。

**Q: 如何修改默认清晰度？**
A: 可以修改 `main.py` 中的 `self.quality_var = tk.StringVar(value="模糊")` 这一行。

**Q: 清晰度设置会保存吗？**
A: 当前版本不会保存设置，每次启动都会重置为默认值。
