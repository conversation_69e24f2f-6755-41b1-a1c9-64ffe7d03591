#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF缩略图生成工具安装脚本
自动安装依赖包并检查环境
"""

import subprocess
import sys
import os


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("错误: 需要Python 3.7或更高版本")
        return False
    
    print("✓ Python版本检查通过")
    return True


def install_requirements():
    """安装依赖包"""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"错误: 找不到 {requirements_file} 文件")
        return False
    
    print("正在安装依赖包...")
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✓ pip已升级到最新版本")
        
        # 安装依赖包
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_file])
        print("✓ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"错误: 安装依赖包失败 - {e}")
        return False


def test_imports():
    """测试导入关键模块"""
    print("正在测试模块导入...")
    
    modules_to_test = [
        ("fitz", "PyMuPDF"),
        ("PIL", "Pillow"),
        ("tkinter", "tkinter (内置模块)")
    ]
    
    all_success = True
    
    for module_name, package_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {package_name} 导入成功")
        except ImportError as e:
            print(f"✗ {package_name} 导入失败: {e}")
            all_success = False
    
    return all_success


def create_test_script():
    """创建测试脚本"""
    test_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import tkinter as tk
from tkinter import messagebox

def test_gui():
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    messagebox.showinfo("测试", "GUI环境测试成功！")
    root.destroy()

if __name__ == "__main__":
    test_gui()
"""
    
    with open("test_gui.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("✓ 测试脚本已创建: test_gui.py")


def main():
    """主安装流程"""
    print("=" * 50)
    print("PDF缩略图生成工具 - 环境安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 安装依赖包
    if not install_requirements():
        input("按回车键退出...")
        return
    
    # 测试模块导入
    if not test_imports():
        print("警告: 部分模块导入失败，程序可能无法正常运行")
    
    # 创建测试脚本
    create_test_script()
    
    print("\n" + "=" * 50)
    print("安装完成！")
    print("=" * 50)
    print("现在你可以:")
    print("1. 运行 'python main.py' 启动主程序")
    print("2. 运行 'python run.py' 使用启动脚本")
    print("3. 运行 'python test_gui.py' 测试GUI环境")
    print("=" * 50)
    
    input("按回车键退出...")


if __name__ == "__main__":
    main()
